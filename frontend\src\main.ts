import { createApp } from "vue";
import App from "./App.vue";
// reset style sheet
import "@/styles/reset.scss";
// CSS device style sheet
import "@/styles/common.scss";
// iconfont css
import "@/assets/iconfont/iconfont.scss";
// iconfontPlus css
import "@/assets/iconfontPlus/iconfont.scss";
// font css
import "@/assets/fonts/font.scss";
// element css
import "element-plus/dist/index.css";
// element dark css
import "element-plus/theme-chalk/dark/css-vars.css";
// custom element dark css
import "@/styles/element-dark.scss";
// custom element css
import "@/styles/element.scss";
// svg icons
import "virtual:svg-icons-register";
// element plus
import ElementPlus from "element-plus";
// element icons
import * as Icons from "@element-plus/icons-vue";
// custom directives
import directives from "@/directives/index";
// vue Router
import router from "@/routers";
// pinia store
import pinia from "@/stores";
// errorHandler
import errorHandler from "@/utils/errorHandler";
// uno.css
import "virtual:uno.css";
// highlight 的样式，依赖包，组件
import "highlight.js/styles/atom-one-dark.css";
import hljsCommon from "highlight.js/lib/common";
import hljsVuePlugin from "@highlightjs/vue-plugin";
//解决谷歌浏览器 Added non-passive event listener to a scroll-blocking 'touchstart' event. Consider markin...
import "default-passive-events";
import contextmenu from "v-contextmenu";
import "v-contextmenu/dist/themes/default.css";
import BLRow from "@/components/Common/BLRow.vue";
import BLCol from "@/components/Common/BLCol.vue";
import { downloadAndInstall } from "@/utils/iconify";
// vue i18n
import I18n from "@/languages/index";
import * as echarts from "echarts/core";
import { LineChart, BarChart, PieChart } from "echarts/charts";
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

echarts.use([LineChart, BarChart, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, CanvasRenderer]);

// 创建应用实例
const app = createApp(App);

// 性能优化：预加载图标
downloadAndInstall();

// 错误处理
app.config.errorHandler = errorHandler;

// 性能优化：按需注册 Element Plus 图标
const iconList = [
  "Edit",
  "Delete",
  "Search",
  "Refresh",
  "Plus",
  "Minus",
  "ColdDrink",
  "Setting",
  "Notification",
  "CircleCheckFilled",
  "ChromeFilled",
  "CircleClose",
  "FolderDelete",
  "Remove",
  "DArrowLeft",
  "DArrowRight",
  "More"
]; // 只注册常用的图标
iconList.forEach(key => {
  app.component(key, Icons[key as keyof typeof Icons]);
});

// 性能优化：延迟加载 highlight.js
setTimeout(() => {
  hljsCommon.highlightAuto("<h1>Highlight.js has been registered successfully!</h1>").value;
}, 0);

// 注册插件和组件
app
  .use(ElementPlus, {
    size: "default",
    zIndex: 3000
  })
  .use(directives)
  .use(router)
  .use(pinia)
  .use(hljsVuePlugin)
  .use(contextmenu)
  .use(I18n);

// 注册全局组件
app.component("BlRow", BLRow).component("BLCol", BLCol);

// 性能优化：使用 requestIdleCallback 在浏览器空闲时挂载应用
if ("requestIdleCallback" in window) {
  (window as any).requestIdleCallback(() => {
    app.mount("#app");
  });
} else {
  setTimeout(() => {
    app.mount("#app");
  }, 0);
}
