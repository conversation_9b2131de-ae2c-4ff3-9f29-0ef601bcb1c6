import { createApp } from "vue";
import App from "./App.vue";

// 基础样式 - 立即加载
import "@/styles/reset.scss";
import "@/styles/common.scss";
import "@/styles/var.scss";

// 核心依赖
import router from "@/routers";
import pinia from "@/stores";
import errorHandler from "@/utils/errorHandler";
import I18n from "@/languages/index";

// 创建应用实例
const app = createApp(App);

// 错误处理
app.config.errorHandler = errorHandler;

// Element Plus 样式
import "element-plus/dist/index.css";
import "virtual:uno.css";

// 同步加载v-contextmenu插件
import contextmenu from "v-contextmenu";
import "v-contextmenu/dist/themes/default.css";

// 注册核心插件
app.use(router).use(pinia).use(I18n).use(contextmenu);

// 挂载应用
app.mount("#app");

// 延迟加载非关键资源
setTimeout(async () => {
  try {
    // 延迟加载样式文件
    await Promise.all([
      import("@/assets/iconfont/iconfont.scss").catch(console.warn),
      import("@/assets/iconfontPlus/iconfont.scss").catch(console.warn),
      import("@/assets/fonts/font.scss").catch(console.warn),
      import("element-plus/theme-chalk/dark/css-vars.css").catch(console.warn),
      import("@/styles/element-dark.scss").catch(console.warn),
      import("@/styles/element.scss").catch(console.warn)
    ]);

    // 延迟加载指令
    const directivesModule = await import("@/directives/index").catch(() => ({ default: null }));
    if (directivesModule.default) {
      app.use(directivesModule.default);
    }

    // 延迟加载图标
    const iconifyModule = await import("@/utils/iconify").catch(() => ({
      downloadAndInstall: () => console.warn("图标加载失败")
    }));
    iconifyModule.downloadAndInstall();
  } catch (error) {
    console.warn("延迟资源加载失败:", error);
  }
}, 100); // 100ms后开始加载非关键资源
