/**
 * 表格和数据优化工具
 */

import { debounce } from "lodash";

/**
 * 创建防抖的localStorage保存函数
 * @param key 存储键
 * @param delay 防抖延迟时间（毫秒）
 * @returns 防抖保存函数
 */
export function createDebouncedSave(key: string, delay: number = 1000) {
  const saveFunction = (data: any) => {
    try {
      // 对于大数据量，只保存必要信息
      const dataToSave =
        Array.isArray(data) && data.length > 100
          ? { ...data, items: [] } // 大数据量时不保存具体项目
          : data;

      localStorage.setItem(key, JSON.stringify(dataToSave));
    } catch (error) {
      console.warn(`保存数据到localStorage失败 (${key}):`, error);
    }
  };

  const debouncedSave = debounce(saveFunction, delay);

  // 返回一个对象，包含防抖函数和flush方法
  return Object.assign(debouncedSave, {
    flush: () => {
      debouncedSave.flush();
    }
  });
}

/**
 * 安全的localStorage读取函数
 * @param key 存储键
 * @param defaultValue 默认值
 * @returns 读取的数据或默认值
 */
export function safeLocalStorageGet<T>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.warn(`从localStorage读取数据失败 (${key}):`, error);
    return defaultValue;
  }
}

/**
 * 事件监听器管理器
 */
export class EventListenerManager {
  private listeners: Map<string, Set<Function>> = new Map();

  /**
   * 添加事件监听器
   */
  add(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(listener);
  }

  /**
   * 移除事件监听器
   */
  remove(event: string, listener: Function): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(listener);
      if (eventListeners.size === 0) {
        this.listeners.delete(event);
      }
    }
  }

  /**
   * 清理所有事件监听器
   */
  cleanup(): void {
    this.listeners.clear();
  }

  /**
   * 获取监听器数量
   */
  getListenerCount(): number {
    let count = 0;
    this.listeners.forEach(listeners => {
      count += listeners.size;
    });
    return count;
  }
}

/**
 * 批量处理数组数据，避免阻塞主线程
 * @param items 要处理的数组
 * @param processor 处理函数
 * @param batchSize 每批处理的数量
 * @param delay 批次间延迟时间（毫秒）
 */
export async function processBatch<T, R>(
  items: T[],
  processor: (item: T, index: number) => R,
  batchSize: number = 100,
  delay: number = 10
): Promise<R[]> {
  const results: R[] = [];

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = batch.map((item, index) => processor(item, i + index));
    results.push(...batchResults);

    // 在批次间添加延迟，让出主线程
    if (i + batchSize < items.length) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return results;
}

/**
 * 创建虚拟滚动配置
 * @param itemCount 总项目数
 * @param itemHeight 每项高度
 * @param containerHeight 容器高度
 * @returns 虚拟滚动配置
 */
export function createVirtualScrollConfig(itemCount: number, itemHeight: number = 54, containerHeight: number = 400) {
  const shouldUseVirtualScroll = itemCount > 50;

  return {
    enabled: shouldUseVirtualScroll,
    itemSize: itemHeight,
    height: shouldUseVirtualScroll ? containerHeight : undefined,
    estimatedItemSize: itemHeight
  };
}

/**
 * 树形数据虚拟化配置
 * @param treeData 树形数据
 * @param itemHeight 每项高度
 * @param containerHeight 容器高度
 * @returns 虚拟化配置
 */
export function createVirtualTreeConfig(treeData: any[], itemHeight: number = 32, containerHeight: number = 400) {
  // 计算总节点数（包括子节点）
  const countNodes = (nodes: any[]): number => {
    let count = 0;
    nodes.forEach(node => {
      count++;
      if (node.children && Array.isArray(node.children)) {
        count += countNodes(node.children);
      }
    });
    return count;
  };

  const totalNodes = countNodes(treeData);
  const shouldUseVirtualization = treeData.length > 50 || totalNodes > 200;

  return {
    enabled: shouldUseVirtualization,
    itemSize: itemHeight,
    height: shouldUseVirtualization ? containerHeight : undefined,
    totalNodes,
    rootNodes: treeData.length
  };
}

/**
 * 表格数据优化器
 */
export class TableDataOptimizer {
  /**
   * 优化表格数据，减少不必要的响应式属性
   */
  static optimizeTableData<T extends Record<string, any>>(data: T[], essentialFields: (keyof T)[]): T[] {
    return data.map(item => {
      const optimized = {} as T;
      essentialFields.forEach(field => {
        optimized[field] = item[field];
      });
      return optimized;
    });
  }

  /**
   * 分页数据，避免一次性渲染大量数据
   */
  static paginateData<T>(data: T[], page: number = 1, pageSize: number = 50): { items: T[]; total: number; hasMore: boolean } {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const items = data.slice(startIndex, endIndex);

    return {
      items,
      total: data.length,
      hasMore: endIndex < data.length
    };
  }
}

/**
 * 内存使用监控
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private memoryThreshold = 100 * 1024 * 1024; // 100MB

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  /**
   * 检查内存使用情况
   */
  checkMemoryUsage(): boolean {
    if ("memory" in performance) {
      const memInfo = (performance as any).memory;
      const usedMemory = memInfo.usedJSHeapSize;

      if (usedMemory > this.memoryThreshold) {
        console.warn(`内存使用过高: ${(usedMemory / 1024 / 1024).toFixed(2)}MB`);
        return false;
      }
    }
    return true;
  }

  /**
   * 建议进行垃圾回收
   */
  suggestGarbageCollection(): void {
    if ("gc" in window && typeof (window as any).gc === "function") {
      (window as any).gc();
    }
  }
}
