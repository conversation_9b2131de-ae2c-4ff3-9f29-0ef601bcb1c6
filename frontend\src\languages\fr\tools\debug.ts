export default {
  search: {
    placeholder: "Rechercher un appareil",
    button: "Rechercher",
    success: "Recherche réussie"
  },
  device2: {
    search: {
      placeholder: "Rechercher un appareil",
      add: "Ajouter un appareil",
      duplicate: "Cette IP et ce port existent déjà, veuillez ne pas les ajouter en double"
    },
    list: {
      empty: "Aucun appareil trouvé",
      unnamed: "Appareil sans nom",
      status: {
        connected: "Connecté",
        disconnected: "Déconnecté"
      },
      contextMenu: {
        connect: "Se connecter",
        edit: "Modifier",
        disconnect: "Se déconnecter",
        remove: "Supprimer"
      },
      message: {
        disconnectFirst: "Veuillez d'abord vous déconnecter avant de modifier",
        disconnectFirstDelete: "Veuillez d'abord vous déconnecter avant de supprimer",
        connectSuccess: "Appareil {name} : Connexion réussie",
        connectExists: "Appareil {name} : Connexion déjà existante",
        connectFailed: "Appareil {name} : Échec de connexion",
        connectFailedReason: "Raison de l'échec de connexion de l'appareil : {reason}",
        disconnected: "Appareil {name} : Déconnecté",
        operationFailed: "Appareil {name} : Échec de l'opération"
      }
    },
    report: {
      group: {
        openWaveConfirm: "Voulez-vous ouvrir le fichier de forme d'onde avec un outil tiers ?",
        tips: "Conseils",
        noWaveTool: "Chemin de l'outil de forme d'onde tiers non configuré"
      },
      common: {
        selectRow: "Veuillez sélectionner la ligne à opérer"
      }
    },
    backup: {
      savePath: "Chemin de Sauvegarde",
      setPath: "Veuillez définir le chemin de sauvegarde",
      setPathTitle: "Définir le chemin de sauvegarde",
      locateFolder: "Localiser le Dossier",
      startBackup: "Démarrer la Sauvegarde",
      cancelBackup: "Annuler la Sauvegarde",
      backup: "Sauvegarde",
      sequence: "Séquence",
      backupType: "Type de Sauvegarde",
      backupDesc: "Description de la Sauvegarde",
      progress: "Progrès",
      status: "Statut",
      noTypeSelected: "Veuillez sélectionner le type de sauvegarde",
      backupSuccess: "Sauvegarde réussie",
      backupFailed: "Sauvegarde échouée",
      openFolderFailed: "Échec de l'ouverture du dossier",
      backupTypes: {
        paramValue: "Valeurs des Paramètres",
        faultInfo: "Informations de Défaut",
        cidConfigPrjLog: "Journal de Projet de Configuration CID",
        waveReport: "Rapport d'Onde"
      },
      backupDescTypes: {
        paramValue: "Sauvegarder toutes les valeurs de configuration des paramètres de l'appareil",
        faultInfo: "Sauvegarder les informations d'enregistrement de défaut de l'appareil",
        cidConfigPrjLog: "Sauvegarder les fichiers de configuration CID et les journaux de projet",
        waveReport: "Sauvegarder les fichiers de rapport d'analyse de forme d'onde"
      },
      backupStatus: {
        userCancelled: "Annulé par l'Utilisateur",
        transferring: "Transfert en cours"
      },
      console: {
        pathNotSet: "Chemin de sauvegarde non défini, impossible de démarrer la sauvegarde",
        noTypeSelected: "Aucun type de sauvegarde sélectionné, impossible de démarrer la sauvegarde",
        startBackup: "Démarrer la sauvegarde, types: {types}, chemin: {path}",
        backupException: "Exception de sauvegarde: {error}",
        pathSelected: "Chemin de sauvegarde sélectionné: {path}",
        pathNotSelected: "Aucun chemin de sauvegarde sélectionné",
        pathNotSetForLocate: "Chemin de sauvegarde non défini, impossible de localiser le dossier",
        folderOpened: "Dossier de sauvegarde ouvert: {path}",
        openFolderFailed: "Échec de l'ouverture du dossier de sauvegarde: {error}",
        taskCompleted: "Tâche terminée",
        taskCancelled: "Tâche annulée",
        typeError: "Type [{type}] erreur: {error}",
        typeCompleted: "Type [{type}] sauvegarde terminée",
        typeCancelled: "Type [{type}] annulé",
        typeFailed: "Type [{type}] échoué"
      }
    },
    remoteControl: {
      directControl: "Contrôle Direct",
      selectControl: "Contrôle Sélectif"
    },
    messageMonitor: {
      title: "Moniteur de Messages",
      start: "Démarrer la Surveillance",
      stop: "Arrêter la Surveillance",
      clear: "Effacer",
      export: "Exporter",
      expand: "Développer",
      collapse: "Réduire",
      close: "Fermer",
      messageType: "Message",
      noMessages: "Aucune donnée de message",
      noMessagesToExport: "Aucune donnée de message à exporter",
      startSuccess: "Surveillance des messages démarrée",
      stopSuccess: "Surveillance des messages arrêtée",
      clearSuccess: "Messages effacés avec succès",
      exportSuccess: "Messages exportés avec succès",
      exportFailed: "Échec de l'exportation des messages",
      toggleFailed: "Échec du changement d'état de surveillance"
    }
  }
};
