export default {
  configure: {
    remoteSet: "遥设"
  },
  console: {
    title: "控制台",
    clear: "清空",
    selectAll: "全选",
    copy: "复制",
    copySuccess: "复制成功",
    noTextSelected: "没有选中的文本",
    copyFailed: "复制失败",
    clearSuccess: "清空控制台",
    collapse: "折叠",
    expand: "展开"
  },
  groupInfo: {
    title: "分组信息",
    table: {
      id: "序号",
      name: "名称",
      desc: "描述",
      fc: "FC",
      count: "数量"
    },
    messages: {
      fetchDataError: "获取数据时发生错误",
      fetchedData: "获取的数据："
    }
  },
  treeClickLog: "点击 treeClick : ",
  contentView: "内容视图",
  emptyDeviceId: "当前装置id为空",
  invalidResponseStructure: "无效的响应结构",
  formattedMenuDataLog: "格式化后的菜单数据 ===",
  allSettings: "全部定值",
  allEditSpSettings: "全部单区定值",
  allEditSgSettings: "全部多区定值",
  deviceTreeDataLog: "装置树数据",
  failedToLoadMenu: "加载装置菜单失败:",
  innerTabs: {
    contentView: "内容视图",
    fileUpload: "文件上传",
    fileDownload: "文件下载",
    deviceTime: "装置对时",
    deviceOperate: "装置操作",
    variableDebug: "变量调试",
    oneClickBackup: "一键备份",
    entryConfig: "词条配置",
    tabClickLog: "标签页点击："
  },
  devices: {
    notConnectedAlt: "装置未连接",
    pleaseConnect: "请先连接装置！"
  },
  list: {
    unnamedDevice: "未命名装置",
    connected: "连接",
    disconnected: "断开",
    connect: "连接",
    edit: "编辑",
    disconnect: "断开",
    remove: "删除",
    noDeviceFound: "没有找到设备",
    handleClickLog: "点击 handleListClick:",
    disconnectBeforeEdit: "请先断开连接再编辑",
    connectSuccess: "装置 {name}：连接成功",
    connectExist: "装置 {name}：连接已存在",
    connectFailed: "装置 {name}：连接失败",
    connectFailedReason: "装置连接失败原因：",
    disconnectedSuccess: "装置 {name}：已断开",
    disconnectedNotify: "装置 {name} 连接已断开",
    currentDisconnectedNotify: "当前装置连接已断开",
    operateFailed: "装置 {name}：操作失败",
    disconnectBeforeDelete: "请先断开连接再删除",
    dataLog: "数据:",
    ipPortExist: "该IP和端口已存在，请勿重复添加",
    messageMonitor: "报文监视",
    connectFirst: "请先连接装置",
    messageMonitorOpened: "装置 {name}：已打开报文监视"
  },
  messageMonitor: {
    title: "报文监视",
    start: "开始监视",
    stop: "停止监视",
    clear: "清空",
    export: "导出",
    expand: "展开",
    collapse: "折叠",
    close: "关闭",
    messageType: "报文",
    noMessages: "暂无报文数据",
    noMessagesToExport: "没有可导出的报文数据",
    startSuccess: "开始监视报文",
    stopSuccess: "停止监视报文",
    clearSuccess: "清空报文成功",
    exportSuccess: "导出报文成功",
    exportFailed: "导出报文失败",
    toggleFailed: "切换监视状态失败",
    pauseScroll: "暂停滚动",
    resumeScroll: "恢复滚动",
    monitoring: "监听中",
    copy: "复制",
    copySuccess: "消息已复制到剪贴板",
    copyFailed: "复制失败",
    autoScrollEnabled: "已开启自动滚动",
    autoScrollDisabled: "已暂停自动滚动",
    send: "发送",
    receive: "接收",
    message: "报文"
  },
  search: {
    placeholder: "搜索装置",
    ipPortExist: "该IP和端口已存在，请勿重复添加"
  },
  summaryPie: {
    other: "其他",
    title: "定值占比",
    subtext: "定值分布"
  },
  deviceInfo: {
    title: "装置信息",
    export: "导出",
    exportTitle: "导出装置信息",
    exportLoading: "正在导出装置基本信息...",
    exportSuccess: "导出装置基本信息成功",
    exportFailed: "导出装置基本信息失败",
    getInfoFailed: "获取装置信息失败。失败原因：{msg}",
    getInfoFailedEmpty: "获取装置信息失败。失败原因：数据为空！",
    defaultFileName: "装置信息.xlsx",
    confirm: "确定",
    tip: "提示"
  },
  allParamSetting: {
    title: "全部定值",
    autoRefresh: "自动刷新",
    refresh: "刷新",
    confirm: "确认",
    import: "导入",
    export: "导出",
    groupTitle: "定值组：",
    allGroups: "全部",
    noDataToImport: "没有需要导入的数据",
    importSuccess: "定值导入成功",
    importFailed: "定值导入失败: {msg}",
    requestFailed: "请求失败，请稍后再试",
    queryFailed: "定值查询失败: {msg}",
    unsavedChanges: "存在未保存的修改，是否继续刷新？",
    confirmButton: "确定",
    cancelButton: "取消",
    alertTitle: "提示",
    errorTitle: "错误",
    noDataToConfirm: "没有需要确认的数据",
    confirmSuccess: "定值更新成功",
    confirmFailed: "定值更新失败: ",
    responseLog: "响应数据:",
    continueAutoRefresh: "继续启用自动刷新",
    settingGroup: "定值组",
    all: "全部",
    minValue: "最小值",
    maxValue: "最大值",
    step: "步长",
    unit: "单位",
    searchNamePlaceholder: "输入定值名称进行搜索",
    searchDescPlaceholder: "输入定值描述进行搜索",
    autoRefreshWarning: "自动刷新开启时不允许修改数据",
    invalidValue: "定值{name}的值{value}不在合法的区间内",
    exportFileName: "装置参数定值_全部定值.xlsx",
    selectPathLog: "选择路径: ",
    exportSuccess: "导出定值列表成功"
  },
  variable: {
    autoRefresh: "自动刷新",
    variableName: "变量名",
    inputVariableName: "请输入待添加的变量名",
    refresh: "刷新",
    add: "新增",
    confirm: "确认",
    import: "导入",
    export: "导出",
    delete: "删除",
    noDataToConfirm: "没有需要确认的数据",
    warning: "告警",
    variableModifiedSuccess: "变量修改成功",
    variableModifiedFailed: "变量修改失败，失败原因：",
    requestFailed: "请求失败，请稍后再试",
    error: "错误",
    success: "成功",
    variableAddSuccess: "变量添加成功",
    variableAddFailed: "变量添加失败，失败原因：",
    variableDeleteSuccess: "变量删除成功",
    variableDeleteFailed: "变量删除失败,失败原因:",
    exportSuccess: "导出装置调试变量信息成功",
    exportFailed: "导出装置调试变量信息失败,失败原因:",
    importSuccess: "导入装置调试变量信息成功",
    importFailed: "导入装置调试变量信息失败:",
    confirmRefresh: "存在未保存的修改，是否继续刷新？",
    confirmAutoRefresh: "存在未保存的修改，是否继续启用自动刷新？",
    pleaseInputVariableName: "请填写变量名",
    exportTitle: "导出装置调试变量",
    importTitle: "导入装置调试变量",
    defaultExportPath: "装置调试变量.xlsx",
    title: "变量调试",
    variableNamePlaceholder: "请输入待添加的变量名",
    batchDelete: "批量删除",
    modifySuccess: "变量修改成功",
    modifyFailed: "变量修改失败，失败原因：{msg}",
    alertTitle: "告警",
    successTitle: "提示",
    confirmButton: "确定",
    cancelButton: "取消",
    sequence: "序号",
    id: "ID",
    name: "名称",
    value: "值",
    type: "类型",
    description: "描述",
    address: "地址",
    operation: "操作",
    enterVariableName: "请输入待添加的变量名",
    responseLog: "响应数据:",
    addSuccess: "变量添加成功",
    addFailed: "变量添加失败，失败原因：",
    addFailedWithName: "变量{name}添加失败: {reason}",
    exportFileName: "装置调试变量.xlsx",
    selectPathLog: "选择路径:",
    exportSuccessLog: "导出装置调试变量信息成功，{path}",
    exportFailedLog: "导出装置调试变量信息失败,失败原因:",
    importFailedLog: "导入装置调试变量信息失败:",
    unsavedChanges: "存在未保存的修改，是否继续刷新？",
    continueAutoRefresh: "继续启用自动刷新",
    tip: "提示",
    sequenceNumber: "序号",
    autoRefreshEditForbidden: "自动刷新模式下禁止编辑",
    warningTitle: "警告",
    invalidNumber: "无效的数值: {value}",
    cancel: "取消"
  },
  backup: {
    sequence: "序号",
    title: "装置备份",
    savePath: "保存路径",
    setPath: "设置备份保存路径",
    setPathTitle: "设置路径",
    startBackup: "开始备份",
    cancelBackup: "取消备份",
    backup: "备份",
    backupType: "备份类型",
    progress: "进度",
    status: "状态",
    operation: "操作",
    backupTypes: {
      paramValue: "备份装置参数定值",
      faultInfo: "备份装置故障报告信息",
      cidConfigPrjLog: "备份cid/ccd/device_config/debug_info/prj/log",
      waveReport: "备份装置录波文件"
    },
    backupDesc: "备份内容说明",
    backupDescTypes: {
      paramValue: "导出装置定值（定值导出.xlsx）",
      faultInfo: "导出装置故障信息（事件/操作/故障/审计报告）",
      cidConfigPrjLog: "导出配置文件（CID/CCD、XML配置、日志文件）",
      waveReport: "导出装置录波文件（/wave/comtrade）"
    },
    locateFolder: "定位文件夹",
    backupSuccess: "备份成功",
    backupFailed: "备份失败",
    openFolderFailed: "打开文件夹失败",
    noTypeSelected: "请先选择备份类型",
    cancelSuccess: "取消成功",
    cancelFailed: "取消失败",
    noBackupToCancel: "当前没有正在进行的备份任务",
    noTaskIdFound: "未找到任务ID，无法取消",
    backupStatus: {
      starting: "开始备份",
      userCancelled: "用户取消",
      transferring: "传输中"
    },
    console: {
      pathNotSet: "未设置备份路径，无法开始备份",
      noTypeSelected: "未选择备份类型，无法开始备份",
      startBackup: "开始备份，类型：{types}, 路径：{path}",
      backupException: "备份异常：{error}",
      pathSelected: "已选择备份路径：{path}",
      pathNotSelected: "未选择备份路径",
      pathNotSetForLocate: "未设置备份路径，无法定位文件夹",
      folderOpened: "已打开备份文件夹：{path}",
      openFolderFailed: "打开备份文件夹失败：{error}",
      taskCompleted: "任务处理完成",
      taskCancelled: "任务已取消",
      typeError: "类型[{type}]发生错误：{error}",
      typeCompleted: "类型[{type}]备份完成",
      typeCancelled: "类型[{type}]已取消",
      typeFailed: "类型[{type}]失败",
      attemptCancel: "尝试取消备份任务",
      noTaskIdFound: "未找到任务ID，无法取消备份",
      cancelSuccess: "备份任务已取消",
      cancelFailed: "取消备份失败：{error}",
      cancelException: "取消备份异常：{error}",
      singleCancelSuccess: "类型[{type}]取消成功",
      singleCancelFailed: "类型[{type}]取消失败：{error}",
      singleCancelException: "类型[{type}]取消异常：{error}"
    }
  },
  operate: {
    title: "装置操作",
    manualWave: "手动录波",
    resetDevice: "装置复归",
    clearReport: "清除报告",
    clearWave: "清除录波",
    executing: "执行中...",
    selectOperation: "请选择操作",
    success: {
      manualWave: "手动录波成功",
      resetDevice: "装置复归成功",
      clearReport: "清除报告成功",
      clearWave: "清除录波成功"
    },
    fail: {
      manualWave: "手动录波失败，失败原因：",
      resetDevice: "装置复归失败，失败原因：",
      clearReport: "清除报告失败，失败原因：",
      clearWave: "清除录波失败，失败原因："
    }
  },
  time: {
    title: "装置对时",
    currentTime: "当前时间",
    deviceTime: "装置时间",
    selectDateTime: "选择日期时间",
    milliseconds: "毫秒",
    now: "此刻",
    read: "读取",
    write: "写入",
    readSuccess: "读取装置时间成功。",
    readFailed: "读取装置时间失败: {msg}",
    readFailedInvalidFormat: "读取装置时间失败: 无效的时间格式",
    readFailedDataError: "读取装置时间失败: 时间数据格式错误",
    writeSuccess: "写入装置时间成功。",
    writeFailed: "写入装置时间失败: {msg}",
    writeFailedInvalidFormat: "写入装置时间失败: 无效的时间格式",
    millisecondsRangeError: "毫秒的取值范围应在0-999之间",
    unknownError: "未知错误"
  },
  reportOperate: {
    title: "报告操作",
    date: "日期：",
    search: "查询",
    save: "保存",
    clearList: "清除列表",
    loading: "数据加载中",
    progress: {
      title: "进度信息",
      loading: "加载中",
      searching: "正在查询{type}"
    },
    table: {
      reportId: "报告编号",
      name: "名称",
      time: "时间",
      operationAddress: "操作地址",
      operationParam: "操作参数",
      value: "值",
      step: "步骤",
      source: "源",
      sourceType: "源类型",
      result: "结果"
    },
    messages: {
      selectDateRange: "请选择完整的时间范围",
      noDataToSave: "无数据可保存",
      saveSuccess: "保存成功",
      saveReport: "保存报告"
    }
  },
  reportGroup: {
    title: "报告组",
    date: "日期：",
    search: "查询",
    save: "保存",
    clearList: "清除列表",
    autoRefresh: "自动刷新",
    loading: "数据加载中",
    progress: {
      title: "进度信息",
      loading: "加载中",
      searching: "正在查询{type}"
    },
    table: {
      reportId: "报告编号",
      time: "时间",
      description: "描述"
    },
    contextMenu: {
      uploadWave: "召唤录波",
      getHistoryReport: "取历史报告",
      saveResult: "保存结果",
      clearContent: "清除页面内容"
    },
    messages: {
      selectDateRange: "请选择完整的时间范围",
      noDataToSave: "无数据可保存",
      noFileToUpload: "无文件可召唤",
      saveSuccess: "保存成功",
      saveReport: "保存报告",
      waveToolNotConfigured: "未配置第三方波形工具路径",
      waveFileUploading: "录波文件召唤中",
      waveFileUploadComplete: "录波文件召唤完成",
      waveFileUploadCompleteWithPath: "录波文件召唤完成，路径：{path}",
      openWaveFileConfirm: "是否通过第三方工具打开波形文件?",
      openWaveFileTitle: "温馨提示",
      confirm: "确定",
      cancel: "取消"
    },
    refresh: {
      stop: "停止刷新",
      start: "自动刷新"
    },
    hiddenItems: {
      show: "显示隐藏条目",
      hide: "不显示隐藏条目"
    }
  },
  fileDownload: {
    title: "文件下载",
    deviceDirectory: "装置目录",
    reboot: "重启",
    noReboot: "不重启",
    selectFile: "选择文件",
    addDownloadFile: "添加待下载的文件",
    addDownloadFolder: "添加待下载的文件夹",
    addDownloadFilesAndFolders: "添加文件和文件夹",
    downloadFile: "下载文件",
    cancelDownload: "取消下载",
    importList: "导入列表",
    exportList: "导出列表",
    batchDelete: "批量删除",
    clearList: "清空列表",
    download: "下载",
    delete: "删除",
    fileName: "文件名称",
    fileSize: "文件大小",
    filePath: "文件路径",
    lastModified: "最后修改时间",
    progress: "进度",
    status: "状态",
    operation: "操作",
    folder: "文件夹",
    waitingDownload: "等待下载",
    calculatingFileInfo: "计算文件信息",
    downloadPreparing: "下载准备",
    downloading: "下载中......",
    downloadComplete: "下载完成",
    downloadError: "下载出错：",
    userCancelled: "用户取消",
    allFilesComplete: "下载完成",
    fileExists: "文件{path}已经存在，添加失败！",
    selectValidFile: "请选择合法的文件进行下载操作",
    remotePathEmpty: "远程路径不能为空",
    noDownloadTask: "没有获取到下载任务无法取消",
    noDownloadInProgress: "当前没有正在进行的下载任务",
    noFilesSelected: "请选择要操作的文件",
    fileSizeZero: "文件 {fileName} 大小为0，无法下载",
    downloadCancelled: "文件{path}下载取消完成",
    downloadCancelledFailed: "文件{path}下载取消失败，失败原因：{msg}",
    fileDeleted: "文件{path}删除完成",
    exportSuccess: "导出下载文件列表成功",
    exportFailed: "导出下载文件列表失败",
    importSuccess: "导入下载文件列表成功",
    importFailed: "导入下载文件列表失败：{msg}",
    downloadList: "下载文件列表",
    exportTitle: "导出下载文件列表",
    importTitle: "导入下载文件列表",
    error: "错误",
    tip: "提示",
    confirm: "确定",
    sequence: "序号",
    confirmButton: "确定",
    cancelButton: "取消",
    alertTitle: "提示",
    errorTitle: "错误",
    successTitle: "成功",
    warningTitle: "警告",
    loading: "加载中",
    executing: "执行中...",
    noData: "暂无数据",
    selectDateRange: "请选择日期范围",
    search: "搜索",
    save: "保存",
    clear: "清空",
    refresh: "刷新",
    stop: "停止",
    start: "开始",
    show: "显示",
    hide: "隐藏",
    showHiddenItems: "显示隐藏条目",
    hideHiddenItems: "隐藏条目",
    continue: "继续",
    cancel: "取消",
    confirmImport: "确认导入",
    confirmExport: "确认导出",
    confirmDelete: "确认删除",
    confirmClear: "确认清空",
    confirmCancel: "确认取消",
    confirmContinue: "确认继续",
    confirmStop: "确认停止",
    confirmStart: "确认开始",
    confirmShow: "确认显示",
    confirmHide: "确认隐藏",
    confirmRefresh: "确认刷新",
    confirmSave: "确认保存",
    confirmSearch: "确认搜索",
    confirmClearList: "确认清空列表",
    confirmImportList: "确认导入列表",
    confirmExportList: "确认导出列表",
    confirmBatchDelete: "确认批量删除",
    confirmDownload: "确认下载",
    confirmCancelDownload: "确认取消下载",
    confirmDeleteFile: "确认删除文件",
    confirmClearFiles: "确认清空文件",
    confirmImportFiles: "确认导入文件",
    confirmExportFiles: "确认导出文件",
    confirmBatchDeleteFiles: "确认批量删除文件",
    confirmDownloadFiles: "确认下载文件",
    confirmCancelDownloadFiles: "确认取消下载文件",
    rename: "下载重命名",
    renamePlaceholder: "下载时重命名（可选）",
    renameCopyFailed: "重命名复制文件失败：",
    packageProgram: "程序打包",
    selectSaveDir: "选择保存目录",
    packageBtn: "打包",
    locateDir: "定位文件夹",
    saveDirEmpty: "请先选择保存目录！",
    packageSuccess: "程序打包完成！",
    packageFailed: "打包失败：{msg}",
    noFileSelected: "请先勾选需要打包的文件！",
    zipPath: "打包路径: {zipPath}",
    addToDownload: "添加到下载界面",
    fileAdded: "文件已添加到下载列表：{path}",
    rebootSuccess: "装置重启成功",
    rebootFailed: "装置重启失败：{msg}"
  },
  fileUpload: {
    serialNumber: "序号",
    title: "文件上传",
    importList: "导入列表",
    exportList: "导出列表",
    batchDelete: "批量删除",
    clearList: "清空列表",
    upload: "上传",
    cancelUpload: "取消上传",
    delete: "删除",
    sequence: "序号",
    fileName: "文件名称",
    fileSize: "文件大小",
    filePath: "文件路径",
    lastModified: "最后修改时间",
    progress: "进度",
    statusTitle: "状态",
    status: {
      waiting: "等待上传",
      preparing: "上传准备",
      uploading: "上传中......",
      completed: "上传完成",
      error: "上传出错：",
      cancelled: "用户取消"
    },
    operation: "操作",
    calculatingFileInfo: "计算文件信息",
    uploadPreparing: "上传准备",
    uploading: "上传中......",
    uploadComplete: "上传完成",
    uploadError: "上传出错：{errorMsg}",
    userCancelled: "用户取消",
    allFilesComplete: "上传完成",
    fileExists: "文件{path}已经存在，添加失败！",
    invalidFile: "请选择合法的文件进行上传操作",
    emptySavePath: "文件保存路径不能为空",
    fileUploadComplete: "文件{fileName}上传完成",
    selectPath: "选择路径",
    pathOptions: {
      shr: "/shr",
      configuration: "/shr/configuration",
      log: "/log",
      wave: "/wave",
      comtrade: "/wave/comtrade"
    },
    deviceDirectory: "装置目录",
    savePath: "保存路径",
    setPath: "设置路径",
    getFiles: "获取文件",
    uploadFiles: "上传文件",
    errors: {
      invalidFile: "请选择合法的文件进行上传操作",
      emptySavePath: "文件保存路径不能为空",
      noUploadTask: "没有获取到上传任务无法取消",
      noUploadInProgress: "当前没有正在进行的上传任务",
      noFilesSelected: "请选择要操作的文件",
      getFilesFailed: "获取装置目录文件失败",
      fileSizeZero: "文件 {fileName} 大小为0，无法上传"
    },
    messages: {
      uploadCompleted: "文件上传完成",
      uploadCancelled: "文件上传取消完成",
      clearListSuccess: "清空文件列表成功"
    }
  },
  info: {
    title: "装置信息",
    export: "导出",
    exportSuccess: "导出装置基本信息成功",
    exportFailed: "导出装置基本信息失败",
    exportTip: "提示",
    confirm: "确定",
    exportLoading: "正在导出装置基本信息...",
    getInfoFailed: "获取装置信息失败。失败原因：",
    dataEmpty: "数据为空！"
  },
  summary: {
    title: "装置分组总览",
    basicInfo: "基本信息",
    settingTotal: "定值总数",
    telemetry: "遥测",
    teleindication: "遥信",
    telecontrol: "遥控",
    driveOutput: "出口传动",
    settingRatio: "定值占比"
  },
  dict: {
    refresh: "刷新",
    confirm: "确认",
    import: "导入",
    export: "导出",
    sequence: "序号",
    shortAddress: "短地址",
    shortAddressTooltip: "输入待搜索的短地址",
    chinese: "中文",
    english: "英文",
    spanish: "西文",
    french: "法文",
    russian: "俄文",
    operation: "操作",
    confirmLog: "确认字典",
    importLog: "导入字典",
    exportLog: "导出字典",
    refreshLog: "刷新字典",
    newValueLog: "新值：",
    loadSuccess: "词条加载成功",
    loadFailed: "词条加载失败",
    saveSuccess: "词条保存成功",
    saveFailed: "词条保存失败",
    partialFailed: "部分词条保存失败",
    noChanges: "没有修改的词条",
    confirmMessage: "确定要保存修改的词条吗？"
  },
  allParamCompare: {
    title: "导入全部定值差异对比",
    cancel: "取消",
    confirm: "确认导入",
    groupName: "组名称",
    name: "名称",
    description: "描述",
    minValue: "最小值",
    maxValue: "最大值",
    step: "步长",
    unit: "单位",
    address: "地址",
    oldValue: "旧值",
    newValue: "新值",
    sequence: "序号",
    searchName: "输入定值名称进行搜索",
    searchDescription: "输入定值描述进行搜索",
    messages: {
      noSelection: "未选中任何数据",
      error: "错误"
    }
  },
  deviceForm: {
    title: {
      add: "添加装置",
      edit: "编辑装置"
    },
    name: "装置名称",
    ip: "IP地址",
    port: "端口",
    connectTimeout: "连接超时时间(毫秒)",
    readTimeout: "全局请求超时时间(毫秒)",
    paramTimeout: "定值修改超时时间(毫秒)",
    encrypted: "加密连接",
    advanced: {
      show: "展开高级选项",
      hide: "收起高级选项"
    },
    buttons: {
      cancel: "取消",
      confirm: "确定"
    },
    messages: {
      nameRequired: "请输入装置名称",
      nameTooLong: "装置名称不宜过长",
      invalidIp: "请输入有效的IP地址",
      invalidPort: "端口号必须在1-65535之间",
      timeoutTooShort: "超时时间不宜太短"
    }
  },
  paramCompare: {
    title: "导入定值差异对比",
    cancel: "取消",
    confirm: "确认导入",
    sequence: "序号",
    name: "名称",
    description: "描述",
    minValue: "最小值",
    maxValue: "最大值",
    step: "步长",
    unit: "单位",
    address: "地址",
    oldValue: "旧值",
    newValue: "新值",
    searchName: "输入定值名称进行搜索",
    searchDescription: "输入定值描述进行搜索",
    messages: {
      noSelection: "未选中任何数据",
      error: "错误"
    }
  },
  progress: {
    title: "进度信息",
    executing: "执行中..."
  },
  remoteYm: {
    title: "远程遥脉",
    sequence: "序号",
    shortAddress: "短地址",
    description: "描述",
    value: "值",
    operation: "操作",
    inputShortAddressFilter: "输入短地址过滤",
    inputDescriptionFilter: "输入描述过滤",
    invalidData: "数据{name}的值{value}不合法",
    error: "错误",
    success: "成功",
    executeSuccess: "执行成功",
    prompt: "提示",
    confirmButton: "确定",
    confirmExecute: "确认执行",
    confirm: "确认",
    executeButton: "执行",
    cancelButton: "取消"
  },
  remoteYt: {
    title: "远程遥调",
    sequence: "序号",
    directControl: "直控",
    selectControl: "选控",
    shortAddress: "短地址",
    description: "描述",
    value: "值",
    operation: "操作",
    inputShortAddressFilter: "输入短地址过滤",
    inputDescriptionFilter: "输入描述过滤",
    invalidData: "数据{name}的值{value}不合法",
    error: "错误",
    success: "成功",
    executeSuccess: "执行成功",
    prompt: "提示",
    confirm: "确定",
    errorInfo: "错误信息",
    executeFailed: "遥调执行失败，失败原因：{msg}",
    executeSuccessLog: "{desc}遥调执行成功",
    cancelSuccess: "取消成功",
    cancelFailed: "遥调取消失败，失败原因：{msg}",
    selectSuccess: "选择成功，是否执行？",
    confirmInfo: "确认信息",
    execute: "执行",
    cancel: "取消"
  },
  paramSetting: {
    title: "装置参数定值",
    autoRefresh: "自动刷新",
    refresh: "刷新",
    confirm: "确认",
    import: "导入",
    export: "导出",
    currentEditArea: "当前运行区",
    selectEditArea: "当前编辑区",
    noDataToImport: "没有需要导入的数据",
    noDataToConfirm: "没有需要确认的数据",
    importSuccess: "定值导入成功",
    importFailed: "定值导入失败",
    updateSuccess: "定值更新成功",
    updateFailed: "定值更新失败",
    requestFailed: "请求失败，请稍后再试",
    setEditArea: "设置",
    setEditAreaTitle: "设置编辑区",
    setEditAreaSuccess: "编辑区设置成功",
    modifiedWarning: "存在未保存的修改，是否继续刷新？",
    autoRefreshWarning: "存在未保存的修改，是否继续启用自动刷新？",
    autoRefreshDisabled: "自动刷新开启时不允许修改数据",
    invalidValue: "定值{name}的值{value}不在合法的区间内",
    exportSuccess: "导出装置参数定值成功",
    exportFailed: "导出装置参数定值失败",
    noDiffData: "未获取到差异数据",
    table: {
      index: "序号",
      name: "名称",
      description: "描述",
      value: "值",
      minValue: "最小值",
      maxValue: "最大值",
      step: "步长",
      address: "地址",
      unit: "单位",
      operation: "操作"
    },
    search: {
      namePlaceholder: "输入定值名称进行搜索",
      descPlaceholder: "输入定值描述进行搜索"
    }
  },
  remoteControl: {
    title: "遥控",
    sequence: "序号",
    shortAddress: "短地址",
    description: "描述",
    control: "控分/控合",
    type: "类型",
    operation: "操作",
    directControl: "直控",
    selectControl: "选控",
    controlClose: "控分",
    controlOpen: "控合",
    noCheck: "不检",
    syncCheck: "检同期",
    deadCheck: "检无压",
    confirmInfo: "确认信息",
    execute: "执行",
    cancel: "取消",
    confirm: "确定",
    success: "成功",
    failed: "失败",
    errorInfo: "错误信息",
    promptInfo: "提示信息",
    confirmSuccess: "选择成功，是否执行？",
    executeSuccess: "执行成功",
    cancelSuccess: "取消成功",
    executeFailed: "遥控执行失败，失败原因：",
    cancelFailed: "遥控取消失败，失败原因：",
    remoteExecuteSuccess: "遥控执行成功",
    remoteCancelSuccess: "遥控取消成功"
  },
  remoteDrive: {
    action: "动作",
    executeSuccess: "执行成功",
    executeFailed: "执行失败",
    prompt: "提示信息",
    error: "错误信息",
    confirm: "确定",
    shortAddress: "短地址",
    description: "描述",
    operation: "操作",
    enterToFilter: "输入短地址过滤",
    enterToFilterDesc: "输入描述过滤",
    actionSuccess: "动作执行成功",
    actionFailed: "动作执行失败",
    failureReason: "失败原因",
    sequence: "序号"
  },
  remoteSignal: {
    autoRefresh: "自动刷新",
    refresh: "刷新",
    export: "导出",
    sequence: "序号",
    name: "名称",
    description: "描述",
    value: "值",
    quality: "品质",
    searchName: "输入名称进行搜索",
    searchDesc: "输入描述进行搜索",
    searchValue: "输入值进行搜索",
    exportTitle: "导出装置遥信信息",
    exportSuccess: "导出装置遥信信息成功",
    exportFailed: "导出装置遥信信息失败",
    exportSuccessWithPath: "导出装置遥信信息成功，",
    exportFailedWithError: "导出装置遥信信息失败：",
    invalidData: "无效的数据：",
    errorInDataCallback: "数据回调处理错误：",
    errorFetchingData: "获取数据时出错："
  },
  remoteTelemetry: {
    autoRefresh: "自动刷新",
    refresh: "刷新",
    export: "导出",
    sequence: "序号",
    name: "名称",
    description: "描述",
    value: "值",
    unit: "单位",
    quality: "品质",
    searchName: "输入名称进行搜索",
    searchDesc: "输入描述进行搜索",
    searchValue: "输入值进行搜索",
    exportTitle: "导出装置状态量信息",
    exportSuccess: "导出装置状态量信息成功",
    exportFailed: "导出装置状态量信息失败",
    exportSuccessWithPath: "导出装置状态量信息成功，",
    exportFailedWithError: "导出装置状态量信息失败：",
    confirm: "确定",
    tip: "提示",
    exportFileName: "装置状态量信息",
    selectPathLog: "选择路径："
  },
  remote: {
    directControl: "直控",
    selectControl: "选控",
    serialNumber: "序号",
    shortAddress: "短地址",
    description: "描述",
    value: "值",
    operation: "操作",
    inputShortAddressFilter: "输入短地址过滤",
    inputDescriptionFilter: "输入描述过滤",
    invalidData: "数据{name}的值{value}不合法",
    error: "错误",
    success: "成功",
    executeSuccess: "执行成功",
    prompt: "提示信息",
    confirm: "确定",
    errorInfo: "错误信息",
    executeFailed: "遥调执行失败，失败原因：{msg}",
    executeSuccessLog: "{desc}遥调执行成功",
    cancelSuccess: "取消成功",
    cancelFailed: "遥调取消失败，失败原因：{msg}",
    selectSuccess: "选择成功，是否执行？",
    confirmInfo: "确认信息",
    execute: "执行",
    cancel: "取消"
  },
  report: {
    uploadWave: "召唤录波",
    searchHistory: "取历史报告",
    saveResult: "保存结果",
    clearContent: "清除页面内容",
    date: "日期",
    query: "查询",
    save: "保存",
    autoRefresh: "自动刷新",
    stopRefresh: "停止刷新",
    clearList: "清除列表",
    progressInfo: "进度信息",
    loading: "数据加载中",
    reportNo: "报告编号",
    time: "时间",
    description: "描述",
    noFileToUpload: "无文件可召唤",
    uploadSuccess: "录波文件召唤完成",
    uploadPath: "录波文件召唤完成，路径：",
    noDataToSave: "无数据可保存",
    saveSuccess: "保存成功",
    saveReport: "保存报告",
    openWaveConfirm: "是否通过第三方工具打开波形文件?",
    confirm: "确定",
    cancel: "取消",
    waveToolNotConfigured: "未配置第三方波形工具路径",
    pleaseSelectTimeRange: "请选择完整的时间范围",
    querying: "正在查询",
    reportNumber: "报告编号",
    operationAddress: "操作地址",
    operationParams: "操作参数",
    result: "结果",
    progress: "进度信息",
    loadingText: "加载中",
    selectCompleteTimeRange: "请选择完整的时间范围",
    fileUploading: "录波文件召唤中",
    fileUploadComplete: "文件召唤完成"
  },
  customMenu: {
    addMenu: "新建自定义菜单",
    editMenu: "编辑自定义菜单",
    deleteMenu: "删除自定义菜单",
    addReport: "新建自定义报告",
    editReport: "编辑自定义报告",
    deleteReport: "删除自定义报告",
    addPointGroup: "新建自定义组",
    editPointGroup: "编辑自定义组",
    deletePointGroup: "删除自定义组",
    selectedPoints: "已选点",
    selectFc: "选择FC",
    selectGroupType: "选择组类型",
    groupTypes: {
      ST: "遥信",
      MX: "遥测",
      SP: "单区定值",
      SG: "多区定值"
    },
    filterPlaceholder: "按名称/描述过滤",
    loadingData: "正在加载数据...",
    noDataForFc: "该FC下暂无数据",
    noDataForGroupType: "该组类型下暂无数据",
    pleaseSelectFc: "请先选择FC",
    pleaseSelectGroupType: "请先选择组类型",
    loadingGroupTypeData: "正在获取组类型数据...",
    loadingGroupTypes: "正在加载组类型的数据...",
    loadedGroupTypes: "已加载组类型",
    dataLoadComplete: "数据加载完成",
    dataLoadFailed: "数据加载失败",
    switchingToGroupType: "正在切换到",
    loadingGroupTypeDataSingle: "正在加载数据...",
    loadGroupTypeFailed: "加载数据失败",
    loadGroupTypeError: "数据时发生错误",
    inputGroupName: "请输入组名称",
    inputGroupDesc: "请输入组描述",
    selectGroupTypeFirst: "请先选择组类型",
    nameValidationFailed: "名称验证失败，请重试",
    nameConflictWithSystem: "名称与系统菜单重复，请使用其他名称",
    nameConflictWithCustom: "名称与现有自定义菜单重复，请使用其他名称",
    refreshData: "刷新数据",
    menuName: "组名",
    menuDesc: "描述",
    reportName: "报告名称",
    reportDesc: "描述",
    reportKeyword: "关键字",
    reportInherit: "继承报告",
    inputMenuName: "请输入组名",
    inputMenuDesc: "请输入描述",
    inputReportName: "请输入报告名称",
    inputReportDesc: "请输入描述",
    inputReportKeyword: "请输入关键字",
    selectReportInherit: "请选择继承报告",
    cancel: "取消",
    confirm: "确定",
    successAddMenu: "新建自定义菜单成功",
    successEditMenu: "编辑自定义菜单成功",
    successDeleteMenu: "删除自定义菜单成功",
    successAddReport: "新建自定义报告成功",
    successEditReport: "编辑自定义报告成功",
    successDeleteReport: "删除自定义报告成功",
    successDeletePointGroup: "删除自定义组成功",
    errorAction: "操作失败",
    errorDelete: "删除失败",
    confirmDeleteMenu: "确定要删除该自定义菜单吗？",
    confirmDeleteReport: "确定要删除该自定义报告吗？",
    confirmDeletePointGroup: "确定要删除该自定义组吗？",
    tip: "提示"
  },
  tree: {
    inputGroupName: "请输入组名称",
    expandAll: "全部展开",
    collapseAll: "全部收缩"
  }
};
