/**
 * 性能监控工具统一导出
 */

// 授权性能监控
export {
  authPerformanceMonitor,
  markCheckAuthStart,
  markCheckAuthEnd,
  markLanguageSyncStart,
  markLanguageSyncEnd,
  markRouteLoadStart,
  markRouteLoadEnd,
  markDeviceControllerInitStart,
  markDeviceControllerInitEnd,
  generateAuthPerformanceReport
} from "./authPerformance";

// 启动性能监控
export { performanceMonitor, markAppMounted, markRouteReady, markResourcesLoaded } from "./performance";

// 组件切换性能监控
export { componentSwitchMonitor, type SwitchMetrics } from "./componentSwitchMonitor";

// 启动缓存管理
export { startupCache, setStartupCache, getStartupCache, clearStartupCache, warmupStartupCache } from "./startupCache";

// 启动性能测试
export { startupTester, runStartupTests, getStartupTestResults } from "./startupTest";

// 表格和数据优化工具
export {
  createDebouncedSave,
  safeLocalStorageGet,
  EventListenerManager,
  processBatch,
  createVirtualScrollConfig,
  TableDataOptimizer,
  MemoryMonitor
} from "./tableOptimization";
