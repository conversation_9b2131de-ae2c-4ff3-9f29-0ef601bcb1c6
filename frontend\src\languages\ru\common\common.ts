export default {
  // Общие операции
  confirm: "Подтвердить",
  cancel: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  save: "Сохранить",
  delete: "Удалить",
  remove: "Убрать",
  edit: "Редактировать",
  add: "Добавить",
  search: "Поиск",
  reset: "Сбро<PERSON>",
  export: "Экспорт",
  import: "Импорт",
  upload: "Загрузить",
  download: "Скачать",
  preview: "Предпросмотр",
  print: "Печать",
  refresh: "Обновить",
  back: "Назад",
  next: "Далее",
  submit: "Отправить",
  loading: "Загрузка...",
  success: "Успешно",
  error: "Ошибка",
  warning: "Предупреждение",
  info: "Информация",
  tip: "Подсказка",
  index: "Номер",
  title: "Заголовок",
  operation: "Операция",
  execute: "Выполнить",
  clear: "Очистить",
  moveUp: "Переместить вверх",
  moveDown: "Переместить вниз",

  // Статус
  status: {
    active: "Активный",
    inactive: "Неактивный",
    enabled: "Включен",
    disabled: "Отключен",
    online: "В сети",
    offline: "Не в сети",
    pending: "В ожидании",
    completed: "Завершено",
    failed: "Неудачно"
  },

  // Время
  time: {
    today: "Сегодня",
    yesterday: "Вчера",
    thisWeek: "На этой неделе",
    lastWeek: "На прошлой неделе",
    thisMonth: "В этом месяце",
    lastMonth: "В прошлом месяце",
    custom: "Пользовательский диапазон"
  },

  // Пагинация
  pagination: {
    total: "Всего",
    items: "элементов",
    page: "страница",
    perPage: "на странице",
    showing: "Показано",
    to: "до",
    of: "из"
  },

  // Валидация формы
  validation: {
    required: "Это поле обязательно для заполнения",
    email: "Пожалуйста, введите действительный адрес электронной почты",
    phone: "Пожалуйста, введите действительный номер телефона",
    number: "Пожалуйста, введите действительное число",
    integer: "Пожалуйста, введите действительное целое число",
    min: "Минимальное значение {min}",
    max: "Максимальное значение {max}",
    length: "Длина должна быть {length}",
    minLength: "Минимальная длина {min}",
    maxLength: "Максимальная длина {max}"
  },

  // Сообщения
  message: {
    saveSuccess: "Сохранение успешно",
    deleteSuccess: "Удаление успешно",
    updateSuccess: "Обновление успешно",
    operationSuccess: "Операция успешна",
    operationFailed: "Операция не удалась",
    confirmDelete: "Вы уверены, что хотите удалить?",
    noData: "Нет данных",
    loading: "Загрузка...",
    networkError: "Ошибка сети, попробуйте еще раз",
    copySuccess: "Копирование успешно"
  },

  // Связанное с языком
  languageSyncWarning: "Синхронизация языка с бэкендом не удалась, но язык фронтенда успешно изменен",

  // Пользовательский селектор файлов
  customFileSelector: {
    title: "Выберите файлы и папки",
    searchPlaceholder: "Поиск файлов или папок...",
    selectedItems: "Выбранные элементы",
    clearAll: "Очистить все",
    noItemsSelected: "Элементы не выбраны",
    cancel: "Отмена",
    confirm: "Подтвердить",
    loading: "Загрузка...",
    error: {
      loadFailed: "Загрузка не удалась",
      accessDenied: "Доступ запрещен",
      notFound: "Путь не найден"
    }
  },

  // Связанное с тестированием
  test: {
    languageSwitch: {
      title: "Тест переключения языка",
      progressDialog: "Тест диалога прогресса",
      showProgress: "Показать диалог прогресса",
      temperatureConverter: "Тест конвертера температуры",
      temperatureDesc: "Следующие названия единиц температуры должны автоматически обновляться после переключения языка:",
      reportNames: "Тест названий отчетов",
      reportDesc: "Следующие названия, связанные с отчетами, должны автоматически обновляться после переключения языка:",
      autoRefresh: "Автообновление",
      showHidden: "Показать скрытые элементы",
      instructions: "Инструкции по тестированию",
      step1: "Нажмите кнопку переключения языка в правом верхнем углу",
      step2: "Выберите другой язык (например, английский, испанский, французский)",
      step3: "Наблюдайте, обновляется ли текст на странице немедленно на новый язык"
    },
    errorPageTest: {
      title: "Тест кнопок страницы ошибок",
      description: "Тестирование отображения кнопок страницы ошибок в различных языковых средах",
      languageSelector: "Выбрать язык",
      buttonPreview: "Предпросмотр кнопки",
      errorPage: "Страница ошибки",
      textLength: "Длина текста",
      testLinks: "Тестовые ссылки",
      goto404: "Перейти на страницу 404",
      goto403: "Перейти на страницу 403",
      goto500: "Перейти на страницу 500",
      comparisonTable: "Таблица сравнения многоязычности",
      language: "Язык",
      buttonText: "Текст кнопки",
      preview: "Предпросмотр"
    }
  }
};
