import path from 'path';
import {BrowserWindow, Notification, app as electronApp} from 'electron';
import {getMainWindow} from 'ee-core/electron';
import Ps, {isProd, getBaseDir} from 'ee-core/ps';
import {getConfig} from 'ee-core/config';
import {isFileProtocol} from 'ee-core/utils';
import {logger} from 'ee-core/log';
import CoreElectronWindow from 'ee-core/electron/window';
import Window from "ee-core/electron/window";
import UtilsIs from "ee-core/utils/is";
import fs from "fs";
import cp from "child_process";
import { ipcMain } from "electron";
import { windowStateService } from "./windowState";
import { t } from "../../data/i18n/i18n";
/**
 * 获取开发工具窗口配置
 */
function getDevToolsConfig() {
  return {
    mode: "detach" as const, // 独立窗口模式
    activate: true, // 立即激活开发工具窗口
  };
}

/**
 * Window
 * @class
 */
class WindowService {
  myNotification: Notification | null;
  windows: { [key: string]: BrowserWindow };

  constructor() {
    this.myNotification = null;
    this.windows = {};

    // 初始化窗口状态管理
    this.initializeWindowStateManagement();
  }

  /**
   * 初始化窗口状态管理
   */
  private initializeWindowStateManagement(): void {
    try {
      windowStateService.initialize();
    } catch (error) {
      logger.error(
        `[WindowService] ${t("services.window.windowStateInitFailed")}:`,
        error
      );
    }
  }

  /**
   * Create a new window
   */
  createWindow(args: {
    type: string;
    content: string;
    windowName: string;
    windowTitle: string;
  }): number {
    const { type, content, windowName, windowTitle } = args;
    let contentUrl: string = "";
    if (type == "html") {
      contentUrl = path.join("file://", getBaseDir(), content);
    } else if (type == "web") {
      contentUrl = content;
    } else if (type == "vue") {
      let addr = "http://localhost:8080";
      if (isProd()) {
        const { mainServer } = getConfig();
        if (
          mainServer &&
          mainServer.protocol &&
          isFileProtocol(mainServer.protocol)
        ) {
          addr =
            mainServer.protocol + path.join(getBaseDir(), mainServer.indexPath);
        }
      }

      contentUrl = addr + content;
    }

    logger.info("[createWindow] url: ", contentUrl);
    const opt = {
      title: windowTitle,
      x: 10,
      y: 10,
      width: 980,
      height: 650,
      frame: false,
      maximizable: true,
      movable: true,
      webPreferences: {
        contextIsolation: false,
        nodeIntegration: true,
      },
    };
    const win = new BrowserWindow(opt);
    const winContentsId = win.webContents.id;
    win.loadURL(contentUrl);
    // win.webContents.openDevTools(); // 调试时自动打开DevTools，现已注释
    this.windows[windowName] = win;

    ipcMain.on("maximize-window", () => this.maximizeWindow());

    return winContentsId;
  }

  /**
   * Get window contents id
   */
  getWCid(args: { windowName: string }): number {
    const { windowName } = args;
    let win: BrowserWindow;
    if (windowName == "main") {
      win = getMainWindow();
    } else {
      win = this.windows[windowName];
    }

    return win.webContents.id;
  }

  /**
   * Realize communication between two windows through the transfer of the main process
   */
  communicate(args: { receiver: string; content: any }): void {
    const { receiver, content } = args;
    if (receiver == "main") {
      const win = getMainWindow();
      win.webContents.send("controller/os/window2ToWindow1", content);
    } else if (receiver == "window2") {
      const win = this.windows[receiver];
      win.webContents.send("controller/os/window1ToWindow2", content);
    }
  }

  /**
   * createNotification
   */
  createNotification(options: any, event: any): void {
    const channel = "controller/os/sendNotification";
    this.myNotification = new Notification(options);

    if (options.clickEvent) {
      this.myNotification.on("click", () => {
        let data = {
          type: "click",
          msg: t("services.window.clickNotification"),
        };
        event.reply(`${channel}`, data);
      });
    }

    if (options.closeEvent) {
      this.myNotification.on("close", () => {
        let data = {
          type: "close",
          msg: t("services.window.closeNotification"),
        };
        event.reply(`${channel}`, data);
      });
    }

    this.myNotification.show();
  }

  /**
   * closeWindow
   */
  async closeWindow(type) {
    const mainWindow = CoreElectronWindow.getMainWindow();
    if (type === "1") {
      // 直接退出
      await electronApp.quit();
    } else if (type === "2") {
      // 最小化托盘
      mainWindow.hide();
    }
  }

  /**
   * maximizeWindow
   */
  async maximizeWindow() {
    const mainWindow = CoreElectronWindow.getMainWindow();
    if (mainWindow.isFullScreen()) {
      mainWindow.setFullScreen(false);
    } else if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }

  /**
   * minimizeWindow
   */
  async minimizeWindow() {
    const mainWindow = CoreElectronWindow.getMainWindow();
    mainWindow.minimize();
  }

  /**
   * 拖拽窗口
   */
  async dragWindow() {
    const mainWindow = CoreElectronWindow.getMainWindow();
    if (mainWindow) {
      // 确保窗口可移动
      mainWindow.setMovable(true);
      // 设置窗口为可拖拽状态
      mainWindow.setWindowButtonVisibility(true);
      // 确保窗口在最前面
      mainWindow.moveTop();
    }
  }

  async printScreen() {
    let softwarePath = path.join(
      Ps.getExtraResourcesDir(),
      "dll",
      "PrintScr.exe"
    );
    const mainWindow = Window.getMainWindow();
    if (!UtilsIs.windows()) {
      return;
    }
    // 检查程序是否存在
    if (!fs.existsSync(softwarePath)) {
      return;
    }
    mainWindow.hide();
    setTimeout(() => {
      const screen = cp.execFile(softwarePath);
      screen.on("exit", () => {
        mainWindow.show();
        mainWindow.moveTop();
      });
    }, 150);
  }

  async openDevTools() {
    try {
      logger.info("[openDevTools] Attempting to open DevTools");

      // 首先尝试获取目标窗口
      const targetWindow =
        BrowserWindow.getFocusedWindow() ||
        BrowserWindow.getAllWindows().find((win) => win.isVisible()) ||
        BrowserWindow.getAllWindows()[0];

      if (!targetWindow) {
        logger.error("[openDevTools] No target window found");
        return;
      }

      const webContents = targetWindow.webContents;

      // 检查webContents是否有效
      if (!webContents || webContents.isDestroyed()) {
        logger.error("[openDevTools] WebContents is invalid or destroyed");
        return;
      }

      // 简化逻辑：直接切换开发工具状态
      if (webContents.isDevToolsOpened()) {
        logger.info("[openDevTools] DevTools already open, closing...");
        webContents.closeDevTools();
      } else {
        logger.info("[openDevTools] Opening DevTools...");

        // 首先尝试使用诊断工具的强制打开方法
        try {
          const diagnosticSuccess = DevToolsDiagnostics.forceOpenDevTools();

          if (!diagnosticSuccess) {
            logger.info(
              "[openDevTools] Diagnostic tool failed, using standard method"
            );
            webContents.openDevTools(getDevToolsConfig());
          }
        } catch (diagnosticError) {
          logger.warn(
            "[openDevTools] Diagnostic tool error, using standard method:",
            diagnosticError
          );
          webContents.openDevTools(getDevToolsConfig());
        }

        // 确保主窗口保持焦点
        setTimeout(() => {
          if (!targetWindow.isDestroyed()) {
            targetWindow.focus();
          }
        }, 100);
      }

      logger.info("[openDevTools] DevTools operation completed successfully");
    } catch (error) {
      logger.error("[openDevTools] Error:", error);

      // 简化的回退方案
      try {
        const mainWindow = getMainWindow();
        if (
          mainWindow &&
          mainWindow.webContents &&
          !mainWindow.webContents.isDestroyed()
        ) {
          logger.info("[openDevTools] Using fallback method");
          if (mainWindow.webContents.isDevToolsOpened()) {
            mainWindow.webContents.closeDevTools();
          } else {
            mainWindow.webContents.openDevTools(getDevToolsConfig());
          }
        }
      } catch (fallbackError) {
        logger.error("[openDevTools] Fallback also failed:", fallbackError);
      }
    }
  }
}

WindowService.toString = () => '[class WindowService]';
const windowService = new WindowService();

export {
    WindowService,
    windowService
} 